'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  TrendingUp,
  TrendingDown,
  Eye,
  Search,
  Building2,
  Download,
  BarChart3,
  Activity,
  Crown,
  Info
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { BenefitRankingAnalytics } from '@/components/benefit-ranking-analytics'

interface SearchTrend {
  rank: number
  search_term: string
  category: string
  search_count: number
  trend_score: number
  change: number
  icon?: string
}

interface TopCompany {
  rank: number
  name: string
  location: string
  industry: string
  view_count: number
  benefit_count: number
  verified_benefit_count: number
  engagement_rate: number
  top_benefits: Array<{ name: string, icon?: string, verified: boolean }>
}

interface OverviewMetrics {
  total_searches: number
  unique_users: number
  company_views: number
  active_companies: number
  avg_engagement: number
}

export function AnalyticsDashboard() {
  const [activeTab, setActiveTab] = useState<'overview' | 'search' | 'companies' | 'rankings' | 'trends'>('overview')
  const [period, setPeriod] = useState('7d')
  const [searchTrends, setSearchTrends] = useState<SearchTrend[]>([])
  const [topCompanies, setTopCompanies] = useState<TopCompany[]>([])
  const [overviewMetrics, setOverviewData] = useState<OverviewMetrics>({
    total_searches: 0,
    unique_users: 0,
    company_views: 0,
    active_companies: 0,
    avg_engagement: 0
  })
  const [loading, setLoading] = useState(false)
  const [isDemoData, _setIsDemoData] = useState(false)
  const [demoNotice, _setDemoNotice] = useState('')
  const [userPaymentStatus, setUserPaymentStatus] = useState<string>('')
  const [mounted, setMounted] = useState(false)

  const fetchAnalytics = useCallback(async () => {
    setLoading(true)
    try {
      const [trendsRes, companiesRes, overviewRes] = await Promise.all([
        fetch(`/api/analytics/search-trends?period=${period}&limit=10`),
        fetch(`/api/analytics/top-companies?period=${period}&limit=10`),
        fetch(`/api/analytics/overview?period=${period}`)
      ])

      const [trendsData, companiesData, overviewData] = await Promise.all([
        trendsRes.json(),
        companiesRes.json(),
        overviewRes.json()
      ])

      setSearchTrends(trendsData.trends || [])
      setTopCompanies(companiesData.companies || [])
      setOverviewData({
        total_searches: overviewData.total_searches || 0,
        unique_users: overviewData.unique_users || 0,
        company_views: overviewData.company_views || 0,
        active_companies: overviewData.active_companies || 0,
        avg_engagement: overviewData.avg_engagement || 0
      })
    } catch (error) {
      console.error('Error fetching analytics:', error)
      // Set default values on error
      setSearchTrends([])
      setTopCompanies([])
      setOverviewData({
        total_searches: 0,
        unique_users: 0,
        company_views: 0,
        active_companies: 0,
        avg_engagement: 0
      })
    } finally {
      setLoading(false)
    }
  }, [period])

  const fetchUserStatus = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUserPaymentStatus(data.user?.paymentStatus || 'free')
      }
    } catch (error) {
      console.error('Error fetching user status:', error)
    }
  }

  useEffect(() => {
    setMounted(true)
    fetchAnalytics()
    fetchUserStatus()
  }, [period, fetchAnalytics])

  const handleExport = async (type: string, format: 'csv' | 'json') => {
    if (isDemoData) {
      alert('Export functionality is only available for Premium users. Upgrade to access real data exports.')
      return
    }

    try {
      const data = type === 'search_trends' ?
        { trends: searchTrends } :
        { companies: topCompanies }

      const response = await fetch('/api/analytics/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type,
          format,
          data,
          filters: { period }
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        const today = mounted ? new Date().toISOString().split('T')[0] : 'export'
        a.download = `workwell_${type}_${today}.${format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        const errorData = await response.json()
        alert(errorData.error || 'Export failed')
      }
    } catch (error) {
      console.error('Error exporting data:', error)
      alert('Export failed. Please try again.')
    }
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'search', label: 'Search Trends', icon: Search },
    { id: 'companies', label: 'Top Companies', icon: Building2 },
    { id: 'rankings', label: 'Benefit Rankings', icon: TrendingUp },
    { id: 'trends', label: 'Insights', icon: Activity },
  ] as const

  const periods = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
  ]

  return (
    <div className="space-y-6">
      {/* Demo Data Notice - Only show for non-paying users */}
      {isDemoData && userPaymentStatus !== 'paying' && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Info className="w-5 h-5 text-amber-600 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-amber-800">Demo Data Preview</h3>
              <p className="text-sm text-amber-700 mt-1">{demoNotice}</p>
              <div className="mt-3">
                <Button
                  size="sm"
                  className="bg-amber-600 hover:bg-amber-700 text-white"
                  onClick={() => window.open('/pricing', '_blank')}
                >
                  <Crown className="w-4 h-4 mr-2" />
                  Upgrade to Premium
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Status Banner for Free Users */}
      {/* {userPaymentStatus === 'free' && !isDemoData && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Crown className="w-5 h-5 text-blue-600" />
              <div>
                <h3 className="text-sm font-medium text-blue-800">Unlock Full Analytics</h3>
                <p className="text-sm text-blue-700">Get access to real-time data, exports, and advanced insights</p>
              </div>
            </div>
            <Button
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-white"
              onClick={() => window.open('/pricing', '_blank')}
            >
              Upgrade Now
            </Button>
          </div>
        </div>
      )} */}

      {/* Controls */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        {/* Mobile-first responsive layout */}
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
          {/* Tab Navigation - Horizontal scroll on mobile */}
          <div className="overflow-x-auto">
            <div className="flex space-x-1 min-w-max">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors whitespace-nowrap ${activeTab === tab.id
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                      }`}
                  >
                    <Icon className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                    <span className="hidden sm:inline">{tab.label}</span>
                    <span className="sm:hidden">{tab.label.split(' ')[0]}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Controls - Stack on mobile */}
          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-3">
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white w-full sm:w-auto"
            >
              {periods.map(p => (
                <option key={p.value} value={p.value}>{p.label}</option>
              ))}
            </select>

            <Button
              onClick={() => fetchAnalytics()}
              variant="outline"
              size="sm"
              disabled={loading}
              className="w-full sm:w-auto"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </Button>
          </div>
        </div>
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Search className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Searches</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {overviewMetrics.total_searches.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Eye className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Company Views</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {overviewMetrics.company_views.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Building2 className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Companies</p>
                  <p className="text-2xl font-bold text-gray-900">{overviewMetrics.active_companies}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <TrendingUp className="w-6 h-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Engagement</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {overviewMetrics.avg_engagement}%
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Insights */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Search Terms</h3>
              <div className="space-y-3">
                {searchTrends.slice(0, 5).map((trend) => (
                  <div key={trend.rank} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-500">#{trend.rank}</span>
                      {trend.icon && <span>{trend.icon}</span>}
                      <span className="font-medium text-gray-900">{trend.search_term}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">{trend.search_count}</span>
                      {trend.change > 0 ? (
                        <TrendingUp className="w-4 h-4 text-green-600" />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-red-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Companies</h3>
              <div className="space-y-3">
                {topCompanies.slice(0, 5).map((company) => (
                  <div key={company.rank} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-500">#{company.rank}</span>
                      <div>
                        <span className="font-medium text-gray-900">
                          {company.name}
                        </span>
                        <p className="text-xs text-gray-500">{company.location}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className="text-sm font-medium text-gray-900">{company.view_count.toLocaleString()}</span>
                      <p className="text-xs text-gray-500">views</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search Trends Tab */}
      {activeTab === 'search' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Search Trends</h3>
            <div className="flex space-x-2">
              <Button
                onClick={() => handleExport('search_trends', 'csv')}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={isDemoData}
                title={isDemoData ? 'Premium feature - Upgrade to export data' : 'Export as CSV'}
              >
                <Download className="w-4 h-4" />
                Export CSV
                {isDemoData && <Crown className="w-3 h-3 ml-1 text-amber-500" />}
              </Button>
              <Button
                onClick={() => handleExport('search_trends', 'json')}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={isDemoData}
                title={isDemoData ? 'Premium feature - Upgrade to export data' : 'Export as JSON'}
              >
                <Download className="w-4 h-4" />
                Export JSON
                {isDemoData && <Crown className="w-3 h-3 ml-1 text-amber-500" />}
              </Button>
            </div>
          </div>

          {/* Mobile-responsive table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Rank</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Search Term</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Searches</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Trend</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Change</th>
                </tr>
              </thead>
              <tbody>
                {searchTrends.map((trend) => (
                  <tr key={trend.rank} className="border-b border-gray-100">
                    <td className="py-3 px-4 text-sm text-gray-900">#{trend.rank}</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        {trend.icon && <span>{trend.icon}</span>}
                        <span className="font-medium text-gray-900">{trend.search_term}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-600 capitalize">
                      {trend.category.replace('_', ' ')}
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-900">
                      {trend.search_count.toLocaleString()}
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-900">{trend.trend_score}</td>
                    <td className="py-3 px-4">
                      <div className={`flex items-center space-x-1 text-sm ${trend.change > 0 ? 'text-green-600' : trend.change < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                        {trend.change > 0 ? (
                          <TrendingUp className="w-4 h-4" />
                        ) : trend.change < 0 ? (
                          <TrendingDown className="w-4 h-4" />
                        ) : null}
                        <span>{trend.change > 0 ? '+' : ''}{trend.change}%</span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile card layout */}
          <div className="md:hidden space-y-4">
            {searchTrends.map((trend) => (
              <div key={trend.rank} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-500">#{trend.rank}</span>
                    {trend.icon && <span>{trend.icon}</span>}
                    <span className="font-medium text-gray-900">{trend.search_term}</span>
                  </div>
                  <div className={`flex items-center space-x-1 text-sm ${trend.change > 0 ? 'text-green-600' : trend.change < 0 ? 'text-red-600' : 'text-gray-600'
                    }`}>
                    {trend.change > 0 ? (
                      <TrendingUp className="w-4 h-4" />
                    ) : trend.change < 0 ? (
                      <TrendingDown className="w-4 h-4" />
                    ) : null}
                    <span>{trend.change > 0 ? '+' : ''}{trend.change}%</span>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Category</p>
                    <p className="font-medium text-gray-900 capitalize">{trend.category.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Searches</p>
                    <p className="font-medium text-gray-900">{trend.search_count.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Trend Score</p>
                    <p className="font-medium text-gray-900">{trend.trend_score}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Companies Tab */}
      {activeTab === 'companies' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Top Companies</h3>
            <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
              <Button
                onClick={() => handleExport('top_companies', 'csv')}
                variant="outline"
                size="sm"
                className="flex items-center justify-center gap-2 w-full sm:w-auto"
                disabled={isDemoData}
                title={isDemoData ? 'Premium feature - Upgrade to export data' : 'Export as CSV'}
              >
                <Download className="w-4 h-4" />
                <span className="hidden sm:inline">Export CSV</span>
                <span className="sm:hidden">CSV</span>
                {isDemoData && <Crown className="w-3 h-3 ml-1 text-amber-500" />}
              </Button>
              <Button
                onClick={() => handleExport('top_companies', 'json')}
                variant="outline"
                size="sm"
                className="flex items-center justify-center gap-2 w-full sm:w-auto"
                disabled={isDemoData}
                title={isDemoData ? 'Premium feature - Upgrade to export data' : 'Export as JSON'}
              >
                <Download className="w-4 h-4" />
                <span className="hidden sm:inline">Export JSON</span>
                <span className="sm:hidden">JSON</span>
                {isDemoData && <Crown className="w-3 h-3 ml-1 text-amber-500" />}
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
            {topCompanies.map((company) => (
              <div key={company.rank} className="border border-gray-200 rounded-lg p-4">
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-3 space-y-2 sm:space-y-0">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-sm font-medium text-gray-500 flex-shrink-0">#{company.rank}</span>
                      <h4 className="font-semibold text-gray-900 truncate">
                        <span className="truncate">{company.name}</span>
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600 truncate">{company.location} • {company.industry}</p>
                  </div>
                  <div className="text-left sm:text-right flex-shrink-0">
                    <p className="text-lg font-bold text-gray-900">{company.view_count.toLocaleString()}</p>
                    <p className="text-xs text-gray-500">views</p>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-2 sm:gap-4 mb-3">
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{company.benefit_count}</p>
                    <p className="text-xs text-gray-500">Benefits</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{company.verified_benefit_count}</p>
                    <p className="text-xs text-gray-500">Verified</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{company.engagement_rate}%</p>
                    <p className="text-xs text-gray-500">Engagement</p>
                  </div>
                </div>

                {company.top_benefits && company.top_benefits.length > 0 && (
                  <div>
                    <p className="text-xs font-medium text-gray-700 mb-2">Top Benefits:</p>
                    <div className="flex flex-wrap gap-1">
                      {company.top_benefits.map((benefit, index) => (
                        <span
                          key={index}
                          className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${benefit.verified
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                            }`}
                        >
                          {benefit.icon && <span className="flex-shrink-0">{benefit.icon}</span>}
                          <span className="truncate">{benefit.name}</span>
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Rankings Tab */}
      {activeTab === 'rankings' && (
        <BenefitRankingAnalytics period={period} />
      )}

      {/* Trends Tab */}
      {activeTab === 'trends' && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Platform Insights</h3>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">🔥 Trending Benefits</h4>
                <p className="text-sm text-blue-800">
                  Wellsport and sabbatical leave are seeing the highest search growth this week
                </p>
              </div>

              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">📈 Growing Industries</h4>
                <p className="text-sm text-green-800">
                  Technology and consulting companies are adding more benefits
                </p>
              </div>

              <div className="p-4 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">🎯 High Engagement</h4>
                <p className="text-sm text-purple-800">
                  Companies with verified benefits see 40% higher engagement rates
                </p>
              </div>

              <div className="p-4 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-2">🌍 Geographic Trends</h4>
                <p className="text-sm text-orange-800">
                  Berlin and Munich companies lead in benefit diversity
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
